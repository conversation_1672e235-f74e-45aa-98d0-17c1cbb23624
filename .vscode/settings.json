{"cSpell.ignorePaths": ["package-lock.json", "node_modules", "vscode-extension", ".git/{info,lfs,logs,refs,objects}/**", ".git/{index,*refs,*HEAD}", ".vscode", ".vscode-insiders", "CHANGELOG.md"], "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.organizeImports": "never", "source.sortImports": "never"}, "editor.formatOnPaste": false, "editor.formatOnSave": false, "editor.formatOnType": false, "editor.quickSuggestions": {"strings": true}, "eslint.rules.customizations": [{"fixable": true, "rule": "*", "severity": "off"}], "eslint.runtime": "node", "eslint.validate": ["astro", "css", "gql", "graphql", "html", "javascript", "javascriptreact", "json", "json5", "jsonc", "less", "markdown", "pcss", "postcss", "scss", "svelte", "toml", "typescript", "typescriptreact", "vue", "xml", "yaml"], "explorer.excludeGitIgnore": true, "explorerExclude.backup": {}, "files.readonlyInclude": {"coverage/**/*": true, "dist/**/*": true, "node_modules/**/*": true, "package-lock.json": true, "CHANGELOG.md": true}, "git.inputValidationSubjectLength": 72, "github.copilot.chat.commitMessageGeneration.instructions": [{"file": ".github/copilot-commit-message-instructions.md"}], "peacock.color": "#f9d094", "prettier.enable": false, "typescript.tsdk": "node_modules/typescript/lib", "workbench.colorCustomizations": {"commandCenter.border": "#15202b99", "sash.hoverBorder": "#fce5c4", "tab.activeBorder": "#fce5c4", "titleBar.activeBackground": "#f9d094", "titleBar.activeForeground": "#15202b", "titleBar.inactiveBackground": "#f9d09499", "titleBar.inactiveForeground": "#15202b99"}}