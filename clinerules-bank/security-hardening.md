# Security Hardening Rules

## Security Principles

### Input Validation

- **Sanitize all inputs**: Never trust user input or external data
- **Validate file paths**: Prevent directory traversal attacks
- **Escape shell arguments**: Use proper escaping for shell commands
- **Validate app names**: Ensure app names don't contain malicious content

### Credential Security

- **Never log passwords**: Passwords should never appear in logs
- **Secure memory handling**: Clear sensitive data from memory when possible
- **Minimal privilege**: Request only necessary permissions
- **Secure prompts**: Use masked input for sensitive information

### File System Security

- **Check permissions**: Verify file permissions before operations
- **Validate paths**: Ensure paths are within expected directories
- **Safe file operations**: Use secure file operation patterns
- **Temporary file handling**: Secure creation and cleanup of temporary files

## Implementation Guidelines

### Shell Command Security

```typescript
// GOOD: Use proper shell escaping
const command = `brew install --cask ${escapeShellArg(appName)}`;

// BAD: Direct string interpolation
const command = `brew install --cask ${appName}`; // Vulnerable to injection

// GOOD: Validate inputs before use
if (!isValidAppName(appName)) {
  throw new ConvertAppsError('Invalid app name', ErrorType.INVALID_INPUT);
}
```

### Password Handling

```typescript
// GOOD: Use secure prompts
const password = await password({
  message: 'Enter your password:',
  mask: true
});

// GOOD: Don't log sensitive information
logger.verbose('Executing sudo command'); // Don't log the actual command with password

// GOOD: Clear sensitive data
try {
  await executeSudoCommand(command, password);
} finally {
  // Clear password from memory if possible
  password = '';
}
```

### File Path Validation

```typescript
// GOOD: Validate and normalize paths
function validateApplicationPath(appPath: string): boolean {
  const normalized = path.normalize(appPath);
  const applicationsDir = path.normalize('/Applications');

  // Ensure path is within Applications directory
  return normalized.startsWith(applicationsDir);
}

// GOOD: Check file permissions
async function checkFileAccess(filePath: string): Promise<boolean> {
  try {
    await fs.access(filePath, fs.constants.R_OK);
    return true;
  } catch {
    return false;
  }
}
```

## Security Checklist

### Input Validation

- [ ] All user inputs are validated before use
- [ ] File paths are validated to prevent directory traversal
- [ ] App names are sanitized before shell command execution
- [ ] Command-line arguments are properly parsed and validated

### Authentication & Authorization

- [ ] Sudo password is handled securely with masked input
- [ ] Password is not logged or stored unnecessarily
- [ ] File permissions are checked before operations
- [ ] Minimal privileges are requested for operations

### Command Execution

- [ ] Shell arguments are properly escaped
- [ ] Commands are constructed safely without injection vulnerabilities
- [ ] Command output is parsed safely
- [ ] Error messages don't leak sensitive information

### File Operations

- [ ] File paths are validated and normalized
- [ ] Directory traversal is prevented
- [ ] File permissions are checked before access
- [ ] Temporary files are created and cleaned up securely

## Common Security Vulnerabilities

### Command Injection

**Risk**: Malicious app names could execute arbitrary commands
**Prevention**: Use `escapeShellArg` utility for all shell arguments
**Example**:

```typescript
// Vulnerable
const command = `rm -rf "${appPath}"`;

// Secure
const command = `rm -rf ${escapeShellArg(appPath)}`;
```

### Path Traversal

**Risk**: Malicious paths could access files outside intended directories
**Prevention**: Validate and normalize all file paths
**Example**:

```typescript
// Vulnerable
const appPath = userInput; // Could be "../../etc/passwd"

// Secure
if (!validateApplicationPath(userInput)) {
  throw new Error('Invalid application path');
}
```

### Information Disclosure

**Risk**: Error messages or logs could reveal sensitive information
**Prevention**: Sanitize error messages and avoid logging sensitive data
**Example**:

```typescript
// Vulnerable
logger.error(`Failed to execute: sudo -S ${command}`); // Logs password

// Secure
logger.error('Failed to execute sudo command'); // Generic message
```

### Privilege Escalation

**Risk**: Unnecessary privilege requests or insecure sudo usage
**Prevention**: Request minimal privileges and validate sudo operations
**Example**:

```typescript
// Good: Only request sudo when actually needed
if (needsSudoAccess(selectedApps)) {
  const password = await promptSudoPassword();
}

// Good: Validate sudo operations
if (!password || password.trim().length === 0) {
  logger.warn('No sudo password provided - skipping privileged operations');
  return;
}
```

## Security Testing

### Test Cases

- **Malicious input**: Test with app names containing shell metacharacters
- **Path traversal**: Test with paths containing ".." sequences
- **Permission errors**: Test behavior when file permissions are denied
- **Invalid passwords**: Test sudo operations with incorrect passwords

### Security Scanning

- **Dependency scanning**: Regularly scan dependencies for vulnerabilities
- **Static analysis**: Use tools to detect potential security issues
- **Code review**: Manual review of security-critical code paths
- **Penetration testing**: Test with malicious inputs and scenarios

## Incident Response

### Security Issue Discovery

1. **Assess impact**: Determine scope and severity of the issue
2. **Immediate mitigation**: Implement temporary fixes if possible
3. **Develop fix**: Create comprehensive fix for the vulnerability
4. **Test thoroughly**: Ensure fix doesn't introduce new issues
5. **Document and communicate**: Update security documentation

### Vulnerability Disclosure

- **Responsible disclosure**: Follow responsible disclosure practices
- **Security advisories**: Create security advisories for serious issues
- **Version updates**: Release security updates promptly
- **User communication**: Inform users of security updates and recommendations
