# Testing Strategy

## Testing Philosophy

### Test-Driven Mindset

- **Test critical paths first**: Focus on user-facing functionality
- **Test error scenarios**: Ensure graceful handling of edge cases
- **Test integration points**: Verify module interactions work correctly
- **Test user workflows**: End-to-end scenarios that users actually experience

### Coverage Goals

- **Maintain >70% statement coverage**: Current project standard
- **100% coverage for critical functions**: Error handling, security, data validation
- **Focus on quality over quantity**: Better to have fewer, comprehensive tests
- **Test behavior, not implementation**: Tests should survive refactoring

## Test Organization

### File Structure

```
src/__tests__/
├── app-scanner.test.ts      # App discovery and Homebrew checking
├── cli.test.ts              # Command-line argument parsing
├── error-handler.test.ts    # Error handling and progress tracking
├── index.test.ts            # Main orchestration and integration
├── installer.test.ts        # Installation logic and dry-run
├── prompts.test.ts          # Interactive user prompts
├── types.test.ts            # Type definitions and error classes
├── utils.test.ts            # Utility functions
└── integration.test.ts      # End-to-end workflow testing
```

### Test Naming Convention

```typescript
describe('Module Name', () => {
  describe('functionName', () => {
    it('should handle normal case successfully', () => {
      // Test implementation
    });

    it('should handle edge case gracefully', () => {
      // Test implementation
    });

    it('should throw appropriate error for invalid input', () => {
      // Test implementation
    });
  });
});
```

## Mocking Strategy

### External Dependencies

- **File System**: Mock `fs.promises` for file operations
- **Child Process**: Mock `child_process.exec` for command execution
- **Inquirer**: Mock `@inquirer/checkbox` and `@inquirer/password`
- **Process**: Mock `process.exit` to prevent test termination

### Mocking Patterns

```typescript
// Mock entire modules
jest.mock('child_process');
jest.mock('fs');

// Mock specific functions with type safety
const mockExec = exec as jest.MockedFunction<typeof exec>;
const mockFsAccess = fs.access as jest.MockedFunction<typeof fs.access>;

// Provide realistic mock implementations
mockExec.mockImplementation((_command, _options, callback) => {
  if (typeof callback === 'function') {
    callback(null, 'mock output', '');
  }
  return {} as any;
});
```

### Mock Data Standards

- **Realistic data**: Use data that matches actual Homebrew output
- **Edge cases**: Include empty results, error conditions, malformed data
- **Consistent naming**: Use descriptive names for mock apps and packages
- **Type safety**: Ensure mock data matches TypeScript interfaces

## Test Categories

### Unit Tests

**Purpose**: Test individual functions and modules in isolation

**Coverage**:

- All public functions in each module
- Error handling for invalid inputs
- Edge cases and boundary conditions
- Type validation and conversion

**Example Focus Areas**:

- App name normalization in `app-scanner.ts`
- Command-line parsing in `cli.ts`
- Error message generation in `error-handler.ts`
- Utility functions in `utils.ts`

### Integration Tests

**Purpose**: Test module interactions and data flow

**Coverage**:

- Complete user workflows from CLI to summary
- Module communication and data passing
- Error propagation between modules
- Configuration passing and transformation

**Example Scenarios**:

- Full installation workflow with mixed success/failure
- Dry-run mode execution across all modules
- User cancellation at different workflow stages
- Error handling integration across modules

### End-to-End Tests

**Purpose**: Test complete user scenarios

**Coverage**:

- Successful installation workflow
- Dry-run preview functionality
- Error recovery scenarios
- User interaction patterns

**Example Workflows**:

- Discover apps → Select apps → Install successfully
- Discover apps → Preview with dry-run → Cancel
- Handle Homebrew not installed → Show help → Exit
- Handle permission errors → Provide guidance → Retry

## Testing Best Practices

### Test Data Management

- **Consistent test apps**: Use same mock app names across tests
- **Realistic scenarios**: Base test data on actual Homebrew packages
- **Edge case coverage**: Include empty lists, single items, large datasets
- **Error conditions**: Test network failures, permission errors, invalid data

### Assertion Patterns

```typescript
// Prefer specific assertions
expect(result.installed).toHaveLength(2);
expect(result.installed[0]!.appName).toBe('Google Chrome');

// Test error conditions thoroughly
expect(() => parseArguments(['--invalid'])).toThrow('Unknown option');

// Verify mock calls with specific parameters
expect(mockExec).toHaveBeenCalledWith(
  'brew install --cask google-chrome firefox',
  expect.any(Object),
  expect.any(Function)
);
```

### Test Isolation

- **Clean state**: Reset mocks and state between tests
- **Independent tests**: Each test should be runnable in isolation
- **No shared state**: Avoid global variables or shared test data
- **Deterministic results**: Tests should always produce same results

## Performance Testing

### Response Time Validation

- **CLI startup time**: Should be <2 seconds
- **App discovery**: Should handle 100+ apps efficiently
- **User interaction**: Should respond to input immediately
- **Installation progress**: Should update at reasonable intervals

### Memory Usage

- **Leak detection**: Use Jest's leak detection for long-running tests
- **Large dataset handling**: Test with realistic app directory sizes
- **Resource cleanup**: Verify proper cleanup of resources

## Continuous Integration

### Test Execution

- **All tests must pass**: No exceptions for CI/CD pipeline
- **Coverage reporting**: Generate and track coverage reports
- **Performance regression**: Monitor test execution time
- **Flaky test detection**: Identify and fix unreliable tests

### Test Environment

- **macOS testing**: Primary target platform
- **Node.js versions**: Test against supported versions (22+)
- **Dependency updates**: Test with latest dependency versions
- **Clean environment**: Fresh npm install for each test run

## Debugging Tests

### Common Issues

- **Timing problems**: Use proper async/await patterns
- **Mock configuration**: Ensure mocks match actual API signatures
- **Test isolation**: Check for shared state between tests
- **Error swallowing**: Ensure errors are properly propagated

### Debugging Tools

- **Jest verbose mode**: Use `--verbose` for detailed output
- **Debug logging**: Add temporary console.log statements
- **Test filtering**: Run specific tests with `--testNamePattern`
- **Coverage analysis**: Use coverage reports to find untested paths
