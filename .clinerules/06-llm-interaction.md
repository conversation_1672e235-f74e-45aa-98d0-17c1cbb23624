# LLM Interaction Guidelines

## Context Awareness

### Project Understanding

- **Always reference project overview**: This is a TypeScript CLI tool for converting macOS apps to Homebrew
- **Understand user goals**: Users want interactive, safe, and efficient app conversion
- **Respect design decisions**: All apps pre-selected by default, dry-run mode, batch operations
- **Maintain consistency**: Follow established patterns and conventions

### Current State Recognition

- **Project is production-ready**: Version 1.0.0 with 123 passing tests
- **Documentation is complete**: README, CHANGELOG, examples all current
- **Build system works**: TypeScript compilation, npm packaging ready
- **Quality standards met**: 74% test coverage, comprehensive error handling

## Code Modification Guidelines

### Before Making Changes

1. **Understand the impact**: How does this change affect user experience?
2. **Check existing tests**: Will current tests still pass?
3. **Consider backwards compatibility**: Will this break existing usage?
4. **Review related modules**: What other files might need updates?

### When Adding Features

- **Follow existing patterns**: Use established error handling, logging, and type patterns
- **Add comprehensive tests**: Include unit tests and integration tests
- **Update documentation**: README, CHANGELOG, and inline documentation
- **Consider user experience**: How does this improve the user workflow?

### When Fixing Bugs

- **Reproduce the issue**: Understand the problem thoroughly
- **Add regression tests**: Prevent the bug from reoccurring
- **Minimal changes**: Fix only what's necessary
- **Verify fix works**: Test the specific scenario that was broken

## Communication Style

### Code Explanations

- **Be specific**: Reference exact file names, function names, and line numbers
- **Explain reasoning**: Why is this approach better than alternatives?
- **Consider trade-offs**: What are the pros and cons of this solution?
- **Provide context**: How does this fit into the larger system?

### Problem Solving

- **Ask clarifying questions**: Ensure you understand the requirement correctly
- **Propose alternatives**: Offer multiple solutions when appropriate
- **Explain implications**: What are the consequences of each approach?
- **Consider edge cases**: What could go wrong with this solution?

## Development Workflow

### Making Changes

1. **Analyze current code**: Understand existing implementation
2. **Plan the change**: Consider impact on other modules
3. **Implement incrementally**: Make small, testable changes
4. **Test thoroughly**: Run existing tests and add new ones
5. **Update documentation**: Keep docs in sync with changes

### Quality Assurance

- **Run all tests**: `npm test` must pass
- **Check TypeScript**: `npx tsc --noEmit` must succeed
- **Verify build**: `npm run build` must complete successfully
- **Test CLI functionality**: Verify the actual user experience

### Code Review Mindset

- **Security implications**: Does this introduce any security risks?
- **Performance impact**: Will this affect CLI startup time or responsiveness?
- **Error handling**: Are all error cases properly handled?
- **User experience**: Does this improve or degrade the user experience?

## Specific Project Considerations

### User Experience Priorities

1. **Safety first**: Dry-run mode and clear previews
2. **Efficiency**: Batch operations and minimal user input required
3. **Clarity**: Clear progress indicators and error messages
4. **Accessibility**: Works for both novice and expert users

### Technical Constraints

- **macOS only**: Don't suggest cross-platform solutions
- **Node.js 22+**: Use modern JavaScript/TypeScript features
- **Homebrew dependency**: Assume Homebrew is the package manager
- **CLI interface**: Focus on command-line user experience

### Existing Architecture

- **Modular design**: Each module has a specific responsibility
- **Type safety**: Comprehensive TypeScript types throughout
- **Error handling**: Custom error classes with specific types
- **Testing strategy**: Comprehensive mocking and integration tests

## Common Scenarios

### Adding New CLI Options

1. Update `cli.ts` with new Commander.js option
2. Add to `CommandOptions` interface in `types.ts`
3. Update help text and documentation
4. Add tests for new option parsing
5. Update README with new option

### Modifying User Interface

1. Consider impact on user workflow
2. Update prompts in `prompts.ts`
3. Ensure consistent visual styling
4. Test with various terminal configurations
5. Update examples in documentation

### Changing Installation Logic

1. Understand current batch operation strategy
2. Maintain dry-run mode compatibility
3. Preserve error handling and recovery
4. Test with various app combinations
5. Update progress tracking if needed

### Enhancing Error Handling

1. Add new error type to `ErrorType` enum
2. Update `ConvertAppsError` usage
3. Add recovery suggestions in `error-handler.ts`
4. Test error scenarios thoroughly
5. Update troubleshooting documentation

## Collaboration Guidelines

### When Uncertain

- **Ask for clarification**: Better to ask than assume
- **Propose multiple options**: Let the user choose the best approach
- **Explain trade-offs**: Help the user make informed decisions
- **Reference existing code**: Point to similar patterns in the codebase

### When Suggesting Changes

- **Start with user benefit**: How does this help users?
- **Consider maintenance burden**: Will this make the code harder to maintain?
- **Respect existing decisions**: Understand why current approach was chosen
- **Propose incremental improvements**: Small, safe changes over large rewrites

### Communication Best Practices

- **Be precise**: Use exact technical terms and references
- **Be helpful**: Provide actionable suggestions and explanations
- **Be respectful**: Acknowledge good existing code and decisions
- **Be thorough**: Consider all aspects of a change, not just the immediate goal
