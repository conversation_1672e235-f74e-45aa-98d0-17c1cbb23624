{"compilerOptions": {"target": "ES2022", "lib": ["ES2022"], "rootDir": "./src", "module": "NodeNext", "moduleResolution": "NodeNext", "resolveJsonModule": true, "allowImportingTsExtensions": false, "strict": true, "exactOptionalPropertyTypes": true, "noImplicitAny": true, "noImplicitOverride": true, "noImplicitReturns": true, "noImplicitThis": true, "noPropertyAccessFromIndexSignature": true, "noUncheckedIndexedAccess": true, "noUnusedLocals": true, "noUnusedParameters": true, "declaration": true, "declarationMap": true, "outDir": "./dist", "removeComments": true, "sourceMap": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "skipLibCheck": true}, "include": ["src/**/*"]}