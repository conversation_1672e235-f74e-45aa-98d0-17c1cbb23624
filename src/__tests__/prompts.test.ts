/**
 * Tests for interactive prompts
 */

import {
  displayFinalSummary,
  displayInstallationPlan,
  promptAppSelection,
  promptConfirmation,
  promptSudoPassword,
} from '../prompts'
import type { AppInfo, CommandOptions } from '../types'

// Mock the inquirer modules
jest.mock('@inquirer/checkbox', () => jest.fn())
jest.mock('@inquirer/password', () => jest.fn())

const mockCheckbox = require('@inquirer/checkbox')
const mockPassword = require('@inquirer/password')

describe('prompts Module', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    // Mock console methods to avoid noise in tests
    jest.spyOn(console, 'log').mockImplementation(() => {})
    jest.spyOn(console, 'error').mockImplementation(() => {})
    jest.spyOn(console, 'warn').mockImplementation(() => {})
  })

  afterEach(() => {
    jest.restoreAllMocks()
  })

  const createMockApp = (
    name: string,
    brewType: 'cask' | 'formula' | 'unavailable' = 'cask',
    status: 'already-installed' | 'available' | 'ignored' | 'unavailable' = 'available',
  ): AppInfo => ({
    alreadyInstalled: status === 'already-installed',
    appPath: `/Applications/${name}.app`,
    brewName: name.toLowerCase().replaceAll(/\s+/g, '-'),
    brewType,
    originalName: name,
    status,
  })

  const mockOptions: CommandOptions = {
    applicationsDir: '/Applications',
    dryRun: false,
    ignore: [],
    verbose: false,
  }

  describe('promptAppSelection', () => {
    it('should return selected apps from checkbox prompt', async () => {
      const apps = [
        createMockApp('Google Chrome', 'cask', 'available'),
        createMockApp('Firefox', 'cask', 'available'),
        createMockApp('Node.js', 'formula', 'available'),
      ]

      const selectedApps = [apps[0]!, apps[2]!] // Select Chrome and Node.js
      mockCheckbox.mockResolvedValue(selectedApps)

      const result = await promptAppSelection(apps, mockOptions)

      expect(result).toEqual(selectedApps)
      expect(mockCheckbox).toHaveBeenCalledWith({
        choices: expect.arrayContaining([
          expect.objectContaining({
            checked: true,
            name: 'Google Chrome (📦 cask)',
            value: apps[0],
          }),
          expect.objectContaining({
            checked: true,
            name: 'Firefox (📦 cask)',
            value: apps[1],
          }),
          expect.objectContaining({
            checked: true,
            name: 'Node.js (⚙️  formula)',
            value: apps[2],
          }),
        ]),
        loop: false,
        message: 'Choose apps to install (use spacebar to toggle, Enter to confirm):',
        pageSize: 15,
        required: false,
      })
    })

    it('should return empty array when no apps are available', async () => {
      const apps = [
        createMockApp('Already Installed', 'cask', 'already-installed'),
        createMockApp('Ignored App', 'cask', 'ignored'),
        createMockApp('Unavailable App', 'unavailable', 'unavailable'),
      ]

      const result = await promptAppSelection(apps, mockOptions)

      expect(result).toEqual([])
      expect(mockCheckbox).not.toHaveBeenCalled()
    })

    it('should return empty array when user selects no apps', async () => {
      const apps = [
        createMockApp('Google Chrome', 'cask', 'available'),
      ]

      mockCheckbox.mockResolvedValue([])

      const result = await promptAppSelection(apps, mockOptions)

      expect(result).toEqual([])
    })

    it('should handle user cancellation', async () => {
      const apps = [
        createMockApp('Google Chrome', 'cask', 'available'),
      ]

      const error = new Error('User cancelled')
      error.name = 'ExitPromptError'
      mockCheckbox.mockRejectedValue(error)

      const result = await promptAppSelection(apps, mockOptions)

      expect(result).toEqual([])
    })

    it('should filter only available apps for selection', async () => {
      const apps = [
        createMockApp('Available App', 'cask', 'available'),
        createMockApp('Already Installed', 'cask', 'already-installed'),
        createMockApp('Ignored App', 'cask', 'ignored'),
        createMockApp('Unavailable App', 'unavailable', 'unavailable'),
      ]

      mockCheckbox.mockResolvedValue([apps[0]!])

      await promptAppSelection(apps, mockOptions)

      expect(mockCheckbox).toHaveBeenCalledWith(
        expect.objectContaining({
          choices: [
            expect.objectContaining({
              checked: true,
              name: 'Available App (📦 cask)',
              value: apps[0],
            }),
          ],
        }),
      )
    })
  })

  describe('promptSudoPassword', () => {
    it('should prompt for password when casks are selected', async () => {
      const selectedApps = [
        createMockApp('Google Chrome', 'cask', 'available'),
        createMockApp('Node.js', 'formula', 'available'),
      ]

      mockPassword.mockResolvedValue('test-password')

      const result = await promptSudoPassword(selectedApps)

      expect(result).toBe('test-password')
      expect(mockPassword).toHaveBeenCalledWith({
        mask: true,
        message: 'Enter your password:',
      })
    })

    it('should return undefined when no casks are selected', async () => {
      const selectedApps = [
        createMockApp('Node.js', 'formula', 'available'),
      ]

      const result = await promptSudoPassword(selectedApps)

      expect(result).toBeUndefined()
      expect(mockPassword).not.toHaveBeenCalled()
    })

    it('should return undefined when empty password is provided', async () => {
      const selectedApps = [
        createMockApp('Google Chrome', 'cask', 'available'),
      ]

      mockPassword.mockResolvedValue('')

      const result = await promptSudoPassword(selectedApps)

      expect(result).toBeUndefined()
    })

    it('should return undefined when password prompt is cancelled', async () => {
      const selectedApps = [
        createMockApp('Google Chrome', 'cask', 'available'),
      ]

      const error = new Error('User cancelled')
      error.name = 'ExitPromptError'
      mockPassword.mockRejectedValue(error)

      const result = await promptSudoPassword(selectedApps)

      expect(result).toBeUndefined()
    })
  })

  describe('displayInstallationPlan', () => {
    it('should display plan for mixed casks and formulas', () => {
      const selectedApps = [
        createMockApp('Google Chrome', 'cask', 'available'),
        createMockApp('Node.js', 'formula', 'available'),
      ]

      displayInstallationPlan(selectedApps, 'test-password', false)

      expect(console.log).toHaveBeenCalledWith(
        expect.stringContaining('📋 Installation Plan'),
      )
    })

    it('should display dry run indicator', () => {
      const selectedApps = [
        createMockApp('Google Chrome', 'cask', 'available'),
      ]

      displayInstallationPlan(selectedApps, 'test-password', true)

      expect(console.log).toHaveBeenCalledWith(
        expect.stringContaining('(DRY RUN)'),
      )
    })

    it('should handle empty app list', () => {
      displayInstallationPlan([], undefined, false)

      // Should not display anything for empty list
      expect(console.log).not.toHaveBeenCalledWith(
        expect.stringContaining('📋 Installation Plan'),
      )
    })

    it('should show sudo warning when no password provided for casks', () => {
      const selectedApps = [
        createMockApp('Google Chrome', 'cask', 'available'),
      ]

      displayInstallationPlan(selectedApps, undefined, false)

      expect(console.log).toHaveBeenCalledWith(
        expect.stringContaining('no sudo access'),
      )
    })
  })

  describe('promptConfirmation', () => {
    it('should return true for confirmation', async () => {
      const result = await promptConfirmation(false)
      expect(result).toBe(true)
    })

    it('should return true for dry run confirmation', async () => {
      const result = await promptConfirmation(true)
      expect(result).toBe(true)
    })
  })

  describe('displayFinalSummary', () => {
    it('should display successful installation summary', () => {
      const selectedApps = [
        createMockApp('Google Chrome', 'cask', 'available'),
        createMockApp('Node.js', 'formula', 'available'),
      ]
      const installedApps = selectedApps
      const failedApps: AppInfo[] = []

      displayFinalSummary(selectedApps, installedApps, failedApps, false)

      expect(console.log).toHaveBeenCalledWith(
        expect.stringContaining('🎉 Installation Complete'),
      )
      expect(console.log).toHaveBeenCalledWith(
        expect.stringContaining('✅ Successfully installed'),
      )
    })

    it('should display dry run summary', () => {
      const selectedApps = [
        createMockApp('Google Chrome', 'cask', 'available'),
        createMockApp('Node.js', 'formula', 'available'),
      ]

      displayFinalSummary(selectedApps, [], [], true)

      expect(console.log).toHaveBeenCalledWith(
        expect.stringContaining('🎉 Dry Run Complete'),
      )
      expect(console.log).toHaveBeenCalledWith(
        expect.stringContaining('📊 Would have processed'),
      )
    })

    it('should display failed installations', () => {
      const selectedApps = [
        createMockApp('Google Chrome', 'cask', 'available'),
      ]
      const installedApps: AppInfo[] = []
      const failedApps = selectedApps

      displayFinalSummary(selectedApps, installedApps, failedApps, false)

      expect(console.log).toHaveBeenCalledWith(
        expect.stringContaining('❌ Failed to install'),
      )
    })

    it('should handle no processed apps', () => {
      displayFinalSummary([], [], [], false)

      expect(console.log).toHaveBeenCalledWith(
        expect.stringContaining('⚠️  No apps were processed'),
      )
    })
  })
})
