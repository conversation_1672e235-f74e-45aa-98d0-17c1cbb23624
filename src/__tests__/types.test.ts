/**
 * Tests for TypeScript types and interfaces
 */

import type {
  AppInfo,
  AppStatus,
  BrewPackageType,
  CommandOptions,
  InstallationResult,
  OperationSummary,
} from '../types'
import {
  ConvertAppsError,
  ErrorType,
} from '../types'

describe('types and Interfaces', () => {
  describe('appInfo interface', () => {
    it('should create a valid AppInfo object', () => {
      const appInfo: AppInfo = {
        alreadyInstalled: false,
        appPath: '/Applications/Google Chrome.app',
        brewName: 'google-chrome',
        brewType: 'cask',
        originalName: 'Google Chrome',
        status: 'available',
      }

      expect(appInfo.originalName).toBe('Google Chrome')
      expect(appInfo.brewName).toBe('google-chrome')
      expect(appInfo.brewType).toBe('cask')
      expect(appInfo.status).toBe('available')
      expect(appInfo.alreadyInstalled).toBe(false)
    })
  })

  describe('commandOptions interface', () => {
    it('should create valid CommandOptions', () => {
      const options: CommandOptions = {
        dryRun: true,
        ignore: ['app1', 'app2'],
        verbose: false,
      }

      expect(options.ignore).toEqual(['app1', 'app2'])
      expect(options.dryRun).toBe(true)
      expect(options.verbose).toBe(false)
    })

    it('should allow optional properties', () => {
      const options: CommandOptions = {}
      expect(options.ignore).toBeUndefined()
      expect(options.dryRun).toBeUndefined()
      expect(options.verbose).toBeUndefined()
    })
  })

  describe('convertAppsError class', () => {
    it('should create a custom error with type', () => {
      const error = new ConvertAppsError(
        'Homebrew not found',
        ErrorType.HOMEBREW_NOT_INSTALLED,
      )

      expect(error.message).toBe('Homebrew not found')
      expect(error.type).toBe(ErrorType.HOMEBREW_NOT_INSTALLED)
      expect(error.name).toBe('ConvertAppsError')
      expect(error instanceof Error).toBe(true)
    })

    it('should include original error if provided', () => {
      const originalError = new Error('Original error')
      const error = new ConvertAppsError(
        'Wrapped error',
        ErrorType.COMMAND_FAILED,
        originalError,
      )

      expect(error.originalError).toBe(originalError)
    })
  })

  describe('type unions', () => {
    it('should accept valid BrewPackageType values', () => {
      const cask: BrewPackageType = 'cask'
      const formula: BrewPackageType = 'formula'
      const unavailable: BrewPackageType = 'unavailable'

      expect(cask).toBe('cask')
      expect(formula).toBe('formula')
      expect(unavailable).toBe('unavailable')
    })

    it('should accept valid AppStatus values', () => {
      const available: AppStatus = 'available'
      const alreadyInstalled: AppStatus = 'already-installed'
      const unavailable: AppStatus = 'unavailable'
      const ignored: AppStatus = 'ignored'

      expect(available).toBe('available')
      expect(alreadyInstalled).toBe('already-installed')
      expect(unavailable).toBe('unavailable')
      expect(ignored).toBe('ignored')
    })
  })

  describe('installationResult interface', () => {
    it('should create a valid InstallationResult', () => {
      const result: InstallationResult = {
        alreadyInstalled: [],
        dryRun: false,
        failed: [],
        ignored: [],
        installed: [],
        unavailable: [],
      }

      expect(Array.isArray(result.installed)).toBe(true)
      expect(Array.isArray(result.failed)).toBe(true)
      expect(Array.isArray(result.alreadyInstalled)).toBe(true)
      expect(Array.isArray(result.ignored)).toBe(true)
      expect(Array.isArray(result.unavailable)).toBe(true)
      expect(result.dryRun).toBe(false)
    })
  })

  describe('operationSummary interface', () => {
    it('should create a valid OperationSummary', () => {
      const summary: OperationSummary = {
        alreadyInstalled: 1,
        availableApps: 8,
        dryRun: false,
        failed: 1,
        ignored: 1,
        installed: 4,
        selected: 5,
        totalApps: 10,
        unavailable: 2,
      }

      expect(summary.totalApps).toBe(10)
      expect(summary.availableApps).toBe(8)
      expect(summary.selected).toBe(5)
      expect(summary.installed).toBe(4)
      expect(summary.failed).toBe(1)
    })
  })
})
