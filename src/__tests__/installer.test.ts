/**
 * Tests for installation logic
 */

import {
  getInstallationSummary,
  installApps,
  validateInstallationPrerequisites,
} from '../installer'
import type { AppInfo, InstallationResult, InstallerConfig } from '../types'

// Mock the entire installer module's internal functions
jest.mock('child_process', () => ({
  exec: jest.fn(),
}))

jest.mock('fs', () => ({
  promises: {
    access: jest.fn(),
  },
}))

// Mock util.promisify to return our mock function
jest.mock('util', () => ({
  promisify: jest.fn(_function => jest.fn().mockImplementation(async (_command: string) =>
  // Default successful response
    ({ stderr: '', stdout: 'Success' }),
  )),
}))

describe('installer Module', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    // Mock console methods to avoid noise in tests
    jest.spyOn(console, 'log').mockImplementation(() => {})
    jest.spyOn(console, 'error').mockImplementation(() => {})
    jest.spyOn(console, 'warn').mockImplementation(() => {})
  })

  afterEach(() => {
    jest.restoreAllMocks()
  })

  const createMockApp = (
    name: string,
    brewType: 'cask' | 'formula' = 'cask',
  ): AppInfo => ({
    alreadyInstalled: false,
    appPath: `/Applications/${name}.app`,
    brewName: name.toLowerCase().replaceAll(/\s+/g, '-'),
    brewType,
    originalName: name,
    status: 'available',
  })

  const mockConfig: InstallerConfig = {
    dryRun: false,
    sudoPassword: 'test-password',
    verbose: false,
  }

  describe('installApps', () => {
    it('should handle empty app list', async () => {
      const result = await installApps([], mockConfig)

      expect(result.installed).toHaveLength(0)
      expect(result.failed).toHaveLength(0)
      expect(result.dryRun).toBe(false)
    })

    it('should work in dry-run mode', async () => {
      const apps = [
        createMockApp('Google Chrome', 'cask'),
      ]

      const dryRunConfig: InstallerConfig = {
        ...mockConfig,
        dryRun: true,
      }

      const result = await installApps(apps, dryRunConfig)

      expect(result.installed).toHaveLength(1)
      expect(result.failed).toHaveLength(0)
      expect(result.dryRun).toBe(true)
      expect(result.installed[0]!.dryRun).toBe(true)
    })
  })

  describe('validateInstallationPrerequisites', () => {
    it('should be a function', () => {
      expect(typeof validateInstallationPrerequisites).toBe('function')
    })
  })

  describe('getInstallationSummary', () => {
    it('should generate summary for successful installations', () => {
      const result: InstallationResult = {
        alreadyInstalled: [],
        dryRun: false,
        failed: [],
        ignored: [],
        installed: [
          {
            appName: 'Google Chrome',
            dryRun: false,
            packageName: 'google-chrome',
            success: true,
          },
        ],
        unavailable: [],
      }

      const summary = getInstallationSummary(result)

      expect(summary).toContain('📊 INSTALLATION SUMMARY')
      expect(summary).toContain('✅ Successfully installed: 1')
      expect(summary).toContain('Google Chrome (google-chrome)')
    })

    it('should generate summary for failed installations', () => {
      const result: InstallationResult = {
        alreadyInstalled: [],
        dryRun: false,
        failed: [
          {
            appName: 'Nonexistent App',
            dryRun: false,
            error: 'Package not found',
            packageName: 'nonexistent-app',
            success: false,
          },
        ],
        ignored: [],
        installed: [],
        unavailable: [],
      }

      const summary = getInstallationSummary(result)

      expect(summary).toContain('❌ Failed to install: 1')
      expect(summary).toContain('Nonexistent App (nonexistent-app): Package not found')
    })

    it('should generate dry-run summary', () => {
      const result: InstallationResult = {
        alreadyInstalled: [],
        dryRun: true,
        failed: [],
        ignored: [],
        installed: [
          {
            appName: 'Google Chrome',
            dryRun: true,
            packageName: 'google-chrome',
            success: true,
          },
        ],
        unavailable: [],
      }

      const summary = getInstallationSummary(result)

      expect(summary).toContain('🔍 DRY RUN SUMMARY')
      expect(summary).toContain('✅ Successfully installed: 1')
    })

    it('should handle empty results', () => {
      const result: InstallationResult = {
        alreadyInstalled: [],
        dryRun: false,
        failed: [],
        ignored: [],
        installed: [],
        unavailable: [],
      }

      const summary = getInstallationSummary(result)

      expect(summary).toContain('⚠️  No packages were processed')
    })
  })
})
